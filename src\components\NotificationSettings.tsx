import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Bell, Clock, Calendar, AlertCircle } from 'lucide-react';
import { useNotificationPreferences } from '@/hooks/useNotificationPreferences';
import { Skeleton } from '@/components/ui/skeleton';

interface NotificationSettingsProps {
  translations?: any;
}

export const NotificationSettings = ({ translations }: NotificationSettingsProps) => {
  const { 
    preferences, 
    loading, 
    updatePreferences, 
    requestNotificationPermission 
  } = useNotificationPreferences();

  const handlePermissionRequest = async () => {
    const granted = await requestNotificationPermission();
    if (granted) {
      await updatePreferences({ browser_notifications_enabled: true });
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-48" />
        <div className="grid gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-64" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-6 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!preferences) return null;

  const notificationPermission = 'Notification' in window ? Notification.permission : 'default';

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Bell className="h-5 w-5" />
        <h2 className="text-xl font-semibold">{translations?.notificationSettings || 'Notification Settings'}</h2>
      </div>

      <div className="grid gap-6">
        {/* Browser Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              {translations?.browserNotifications || 'Browser Notifications'}
            </CardTitle>
            <CardDescription>
              {translations?.browserNotificationsDesc || 'Receive notifications directly in your browser'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {notificationPermission === 'denied' && (
              <div className="flex items-center gap-2 text-sm text-destructive">
                <AlertCircle className="h-4 w-4" />
                Browser notifications are blocked. Please enable them in your browser settings.
              </div>
            )}
            
            {notificationPermission === 'default' && (
              <Button onClick={handlePermissionRequest} variant="outline">
                Enable Browser Notifications
              </Button>
            )}
            
            {notificationPermission === 'granted' && (
              <div className="flex items-center space-x-2">
                <Switch
                  id="browser-notifications"
                  checked={preferences.browser_notifications_enabled}
                  onCheckedChange={(checked) => 
                    updatePreferences({ browser_notifications_enabled: checked })
                  }
                />
                <Label htmlFor="browser-notifications">
                  {translations?.enableBrowserNotifications || 'Enable browser notifications'}
                </Label>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Expense Reminders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              {translations?.expenseReminders || 'Expense Reminders'}
            </CardTitle>
            <CardDescription>
              {translations?.expenseRemindersDesc || 'Get reminded to log your daily expenses'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="expense-reminders"
                checked={preferences.expense_reminder_enabled}
                onCheckedChange={(checked) => 
                  updatePreferences({ expense_reminder_enabled: checked })
                }
              />
              <Label htmlFor="expense-reminders">
                {translations?.enableExpenseReminders || 'Enable expense reminders'}
              </Label>
            </div>
            
            {preferences.expense_reminder_enabled && (
              <div className="flex items-center space-x-2">
                <Label htmlFor="reminder-hours" className="text-sm">
                  {translations?.remindMeEvery || 'Remind me every'}
                </Label>
                <Input
                  id="reminder-hours"
                  type="number"
                  min="1"
                  max="168"
                  value={preferences.expense_reminder_hours}
                  onChange={(e) => 
                    updatePreferences({ expense_reminder_hours: parseInt(e.target.value) || 24 })
                  }
                  className="w-20"
                />
                <Label className="text-sm">
                  {translations?.hours || 'hours'}
                </Label>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recurring Bill Reminders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              {translations?.recurringBillReminders || 'Recurring Bill Reminders'}
            </CardTitle>
            <CardDescription>
              {translations?.recurringBillRemindersDesc || 'Get notified before recurring expenses are due'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="bill-reminders"
                checked={preferences.recurring_bill_reminder_enabled}
                onCheckedChange={(checked) => 
                  updatePreferences({ recurring_bill_reminder_enabled: checked })
                }
              />
              <Label htmlFor="bill-reminders">
                {translations?.enableBillReminders || 'Enable bill reminders'}
              </Label>
            </div>
            
            {preferences.recurring_bill_reminder_enabled && (
              <div className="flex items-center space-x-2">
                <Label htmlFor="reminder-days" className="text-sm">
                  {translations?.remindMeDays || 'Remind me'}
                </Label>
                <Input
                  id="reminder-days"
                  type="number"
                  min="1"
                  max="30"
                  value={preferences.recurring_bill_reminder_days}
                  onChange={(e) => 
                    updatePreferences({ recurring_bill_reminder_days: parseInt(e.target.value) || 1 })
                  }
                  className="w-20"
                />
                <Label className="text-sm">
                  {translations?.daysBefore || 'days before due'}
                </Label>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};