import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Tables } from '@/integrations/supabase/types';

type RecurringExpense = Tables<'recurring_expenses'>;

export const useRecurringExpenses = () => {
  const [recurringExpenses, setRecurringExpenses] = useState<RecurringExpense[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchRecurringExpenses = async () => {
    try {
      const { data, error } = await supabase
        .from('recurring_expenses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setRecurringExpenses(data || []);
    } catch (error) {
      console.error('Error fetching recurring expenses:', error);
      toast({
        title: "Error",
        description: "Failed to fetch recurring expenses",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const addRecurringExpense = async (expense: {
    amount: number;
    category: { id: string; name: string; emoji: string };
    frequency: string;
    frequencyInterval: number;
    startDate: string;
    endDate?: string;
    note: string;
  }) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Calculate next due date
      const { data: nextDueDate } = await supabase.rpc('calculate_next_due_date', {
        base_date: expense.startDate,
        frequency: expense.frequency,
        frequency_interval: expense.frequencyInterval
      });

      const { error } = await supabase
        .from('recurring_expenses')
        .insert({
          user_id: user.id,
          amount: expense.amount,
          category_id: expense.category.id,
          category_name: expense.category.name,
          category_emoji: expense.category.emoji,
          frequency: expense.frequency,
          frequency_interval: expense.frequencyInterval,
          start_date: expense.startDate,
          end_date: expense.endDate || null,
          next_due_date: nextDueDate,
          note: expense.note
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Recurring expense created successfully"
      });

      fetchRecurringExpenses();
    } catch (error) {
      console.error('Error adding recurring expense:', error);
      toast({
        title: "Error",
        description: "Failed to create recurring expense",
        variant: "destructive"
      });
    }
  };

  const updateRecurringExpense = async (id: string, updates: Partial<RecurringExpense>) => {
    try {
      const { error } = await supabase
        .from('recurring_expenses')
        .update(updates)
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Recurring expense updated successfully"
      });

      fetchRecurringExpenses();
    } catch (error) {
      console.error('Error updating recurring expense:', error);
      toast({
        title: "Error",
        description: "Failed to update recurring expense",
        variant: "destructive"
      });
    }
  };

  const deleteRecurringExpense = async (id: string) => {
    try {
      const { error } = await supabase
        .from('recurring_expenses')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Recurring expense deleted successfully"
      });

      fetchRecurringExpenses();
    } catch (error) {
      console.error('Error deleting recurring expense:', error);
      toast({
        title: "Error",
        description: "Failed to delete recurring expense",
        variant: "destructive"
      });
    }
  };

  useEffect(() => {
    fetchRecurringExpenses();
  }, []);

  return {
    recurringExpenses,
    loading,
    addRecurringExpense,
    updateRecurringExpense,
    deleteRecurringExpense,
    refetch: fetchRecurringExpenses
  };
};