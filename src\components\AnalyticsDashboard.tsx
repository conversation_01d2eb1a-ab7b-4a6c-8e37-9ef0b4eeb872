import { useMemo } from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TrendingUp, TrendingDown, PieChart, BarChart3, Calendar, DollarSign } from 'lucide-react';
import { useExpenses } from '@/hooks/useExpenses';
import { useBudgets } from '@/hooks/useBudgets';
import { useLanguage } from '@/hooks/useLanguage';

export const AnalyticsDashboard = () => {
  const { summary, allExpenses, categories } = useExpenses();
  const { budgets } = useBudgets();
  const { language, isRTL } = useLanguage();

  const translations = {
    en: {
      title: "Analytics Dashboard",
      overview: "Overview",
      trends: "Trends",
      categories: "Categories",
      budgets: "Budget Status",
      totalSpent: "Total Spent",
      avgPerDay: "Daily Average",
      avgPerTransaction: "Avg Transaction",
      totalTransactions: "Transactions",
      spendingTrend: "Spending Trend",
      categoryBreakdown: "Category Breakdown",
      budgetProgress: "Budget Progress",
      last7Days: "Last 7 Days",
      last30Days: "Last 30 Days",
      thisMonth: "This Month",
      lastMonth: "Last Month",
      noData: "No data available",
      addExpenses: "Add some expenses to see analytics",
      onBudget: "On Budget",
      overBudget: "Over Budget",
      remaining: "Remaining"
    },
    ar: {
      title: "لوحة التحليلات",
      overview: "نظرة عامة",
      trends: "الاتجاهات",
      categories: "الفئات",
      budgets: "حالة الميزانية",
      totalSpent: "إجمالي المصروفات",
      avgPerDay: "المعدل اليومي",
      avgPerTransaction: "متوسط المعاملة",
      totalTransactions: "المعاملات",
      spendingTrend: "اتجاه الإنفاق",
      categoryBreakdown: "تفصيل الفئات",
      budgetProgress: "تقدم الميزانية",
      last7Days: "آخر 7 أيام",
      last30Days: "آخر 30 يوم",
      thisMonth: "هذا الشهر",
      lastMonth: "الشهر الماضي",
      noData: "لا توجد بيانات متاحة",
      addExpenses: "أضف بعض المصروفات لرؤية التحليلات",
      onBudget: "ضمن الميزانية",
      overBudget: "تجاوز الميزانية",
      remaining: "المتبقي"
    }
  };

  const t = translations[language];

  // Calculate trends
  const trends = useMemo(() => {
    const now = new Date();
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const last60Days = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const last7DaysSpending = allExpenses
      .filter(e => new Date(e.date) >= last7Days)
      .reduce((sum, e) => sum + e.amount, 0);

    const previous7DaysSpending = allExpenses
      .filter(e => new Date(e.date) >= new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000) && new Date(e.date) < last7Days)
      .reduce((sum, e) => sum + e.amount, 0);

    const last30DaysSpending = allExpenses
      .filter(e => new Date(e.date) >= last30Days)
      .reduce((sum, e) => sum + e.amount, 0);

    const previous30DaysSpending = allExpenses
      .filter(e => new Date(e.date) >= last60Days && new Date(e.date) < last30Days)
      .reduce((sum, e) => sum + e.amount, 0);

    const weeklyTrend = previous7DaysSpending === 0 ? 0 : 
      ((last7DaysSpending - previous7DaysSpending) / previous7DaysSpending) * 100;

    const monthlyTrend = previous30DaysSpending === 0 ? 0 : 
      ((last30DaysSpending - previous30DaysSpending) / previous30DaysSpending) * 100;

    return {
      weeklyTrend,
      monthlyTrend,
      last7DaysSpending,
      last30DaysSpending
    };
  }, [allExpenses]);

  // Calculate daily averages
  const dailyAverage = useMemo(() => {
    if (allExpenses.length === 0) return 0;
    const daysSinceFirst = Math.max(1, 
      Math.ceil((new Date().getTime() - new Date(allExpenses[allExpenses.length - 1]?.date || new Date()).getTime()) / (1000 * 60 * 60 * 24))
    );
    return summary.total / daysSinceFirst;
  }, [allExpenses, summary.total]);

  // Top categories
  const topCategories = useMemo(() => {
    return Object.entries(summary.categoryBreakdown)
      .map(([categoryId, amount]) => {
        const category = categories.find(c => c.id === categoryId);
        return { category, amount, percentage: (amount / summary.total) * 100 };
      })
      .filter(item => item.category)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);
  }, [summary.categoryBreakdown, categories, summary.total]);

  if (allExpenses.length === 0) {
    return (
      <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
        <Card className="bg-gradient-card border-0 shadow-card">
          <CardContent className="p-8 text-center">
            <BarChart3 className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">{t.noData}</h3>
            <p className="text-muted-foreground">{t.addExpenses}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <Card className="bg-gradient-card border-0 shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            <BarChart3 className="h-5 w-5 text-primary" />
            {t.title}
          </CardTitle>
        </CardHeader>
      </Card>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-card/50 backdrop-blur-sm border border-border/50">
          <TabsTrigger value="overview">{t.overview}</TabsTrigger>
          <TabsTrigger value="trends">{t.trends}</TabsTrigger>
          <TabsTrigger value="categories">{t.categories}</TabsTrigger>
          <TabsTrigger value="budgets">{t.budgets}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="p-4 bg-gradient-card border-0 shadow-card">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <DollarSign className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t.totalSpent}</p>
                  <p className="text-xl font-bold">${summary.total.toFixed(2)}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 bg-gradient-card border-0 shadow-card">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Calendar className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t.avgPerDay}</p>
                  <p className="text-xl font-bold">${dailyAverage.toFixed(2)}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 bg-gradient-card border-0 shadow-card">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t.avgPerTransaction}</p>
                  <p className="text-xl font-bold">
                    ${allExpenses.length > 0 ? (summary.total / allExpenses.length).toFixed(2) : '0.00'}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 bg-gradient-card border-0 shadow-card">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t.totalTransactions}</p>
                  <p className="text-xl font-bold">{allExpenses.length}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Recent Activity Summary */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6 bg-gradient-card border-0 shadow-card">
              <h3 className="text-lg font-semibold mb-4">{t.last7Days}</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Amount:</span>
                  <span className="font-semibold">${trends.last7DaysSpending.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Trend:</span>
                  <div className="flex items-center gap-1">
                    {trends.weeklyTrend >= 0 ? (
                      <TrendingUp className="h-4 w-4 text-destructive" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-primary" />
                    )}
                    <span className={trends.weeklyTrend >= 0 ? 'text-destructive' : 'text-primary'}>
                      {Math.abs(trends.weeklyTrend).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6 bg-gradient-card border-0 shadow-card">
              <h3 className="text-lg font-semibold mb-4">{t.last30Days}</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Amount:</span>
                  <span className="font-semibold">${trends.last30DaysSpending.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Trend:</span>
                  <div className="flex items-center gap-1">
                    {trends.monthlyTrend >= 0 ? (
                      <TrendingUp className="h-4 w-4 text-destructive" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-primary" />
                    )}
                    <span className={trends.monthlyTrend >= 0 ? 'text-destructive' : 'text-primary'}>
                      {Math.abs(trends.monthlyTrend).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card className="p-6 bg-gradient-card border-0 shadow-card">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              {t.spendingTrend}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium">{t.last7Days}</h4>
                <div className="text-3xl font-bold text-primary">
                  ${trends.last7DaysSpending.toFixed(2)}
                </div>
                <div className="flex items-center gap-2">
                  {trends.weeklyTrend >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-destructive" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-primary" />
                  )}
                  <span className={`text-sm ${trends.weeklyTrend >= 0 ? 'text-destructive' : 'text-primary'}`}>
                    {trends.weeklyTrend >= 0 ? '+' : ''}{trends.weeklyTrend.toFixed(1)}% vs previous week
                  </span>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-medium">{t.last30Days}</h4>
                <div className="text-3xl font-bold text-primary">
                  ${trends.last30DaysSpending.toFixed(2)}
                </div>
                <div className="flex items-center gap-2">
                  {trends.monthlyTrend >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-destructive" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-primary" />
                  )}
                  <span className={`text-sm ${trends.monthlyTrend >= 0 ? 'text-destructive' : 'text-primary'}`}>
                    {trends.monthlyTrend >= 0 ? '+' : ''}{trends.monthlyTrend.toFixed(1)}% vs previous month
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card className="p-6 bg-gradient-card border-0 shadow-card">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {t.categoryBreakdown}
            </h3>
            <div className="space-y-4">
              {topCategories.map(({ category, amount, percentage }) => {
                if (!category) return null;
                
                return (
                  <div key={category.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{category.emoji}</span>
                        <div>
                          <p className="font-medium">{category.name}</p>
                          <p className="text-sm text-muted-foreground">{percentage.toFixed(1)}%</p>
                        </div>
                      </div>
                      <p className="font-bold text-lg">${amount.toFixed(2)}</p>
                    </div>
                    
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="h-2 bg-primary rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="budgets" className="space-y-6">
          <Card className="p-6 bg-gradient-card border-0 shadow-card">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {t.budgetProgress}
            </h3>
            {budgets.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No budgets configured yet
              </p>
            ) : (
              <div className="space-y-4">
                {budgets.map((budget) => (
                  <div key={budget.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-xl">{budget.category.emoji}</span>
                        <div>
                          <p className="font-medium">{budget.category.name}</p>
                          <p className="text-sm text-muted-foreground">
                            ${budget.spent.toFixed(2)} / ${budget.amount.toFixed(2)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${budget.isOverBudget ? 'text-destructive' : 'text-primary'}`}>
                          {budget.isOverBudget ? t.overBudget : t.onBudget}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t.remaining}: ${Math.max(0, budget.remaining).toFixed(2)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="w-full bg-muted rounded-full h-3">
                      <div 
                        className={`h-3 rounded-full transition-all duration-500 ease-out ${
                          budget.isOverBudget ? 'bg-destructive' : 'bg-primary'
                        }`}
                        style={{ width: `${Math.min(budget.percentage, 100)}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};