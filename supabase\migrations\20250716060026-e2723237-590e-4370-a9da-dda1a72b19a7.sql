-- Create notification_preferences table
CREATE TABLE public.notification_preferences (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  expense_reminder_enabled BOOLEAN NOT NULL DEFAULT true,
  expense_reminder_hours INTEGER NOT NULL DEFAULT 24, -- Hours without logging before reminder
  recurring_bill_reminder_enabled BOOLEAN NOT NULL DEFAULT true,
  recurring_bill_reminder_days INTEGER NOT NULL DEFAULT 1, -- Days before due date to remind
  browser_notifications_enabled BOOLEAN NOT NULL DEFAULT false,
  email_notifications_enabled BOOLEAN NOT NULL DEFAULT false,
  last_expense_logged_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies for user access
CREATE POLICY "Users can view their own notification preferences" 
ON public.notification_preferences 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own notification preferences" 
ON public.notification_preferences 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification preferences" 
ON public.notification_preferences 
FOR UPDATE 
USING (auth.uid() = user_id);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_notification_preferences_updated_at
  BEFORE UPDATE ON public.notification_preferences
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to update last expense logged timestamp
CREATE OR REPLACE FUNCTION public.update_last_expense_logged()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the last_expense_logged_at timestamp when a new expense is added
  INSERT INTO public.notification_preferences (user_id, last_expense_logged_at)
  VALUES (NEW.user_id, NEW.created_at)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    last_expense_logged_at = NEW.created_at,
    updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update last expense logged when expense is added
CREATE TRIGGER update_last_expense_logged_trigger
  AFTER INSERT ON public.expenses
  FOR EACH ROW
  EXECUTE FUNCTION public.update_last_expense_logged();