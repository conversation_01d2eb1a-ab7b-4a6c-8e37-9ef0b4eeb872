-- Create a cron job to run the recurring expenses processor daily at 6:00 AM
SELECT cron.schedule(
  'process-recurring-expenses-daily',
  '0 6 * * *', -- 6:00 AM every day
  $$
  SELECT
    net.http_post(
        url:='https://wbjkmobvzvlztcklxjgw.supabase.co/functions/v1/process-recurring-expenses',
        headers:='{"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indiamttb2J2enZsenRja2x4amd3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2NDMyNTMsImV4cCI6MjA2ODIxOTI1M30.i5xHy2uHF7_xG_WsEzig8A_UBBQWiFkiSAGFPyLKqfM"}'::jsonb,
        body:='{"scheduled": true}'::jsonb
    ) as request_id;
  $$
);