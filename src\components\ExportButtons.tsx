import { Printer, Download, FileSpreadsheet } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useExpenses } from '@/hooks/useExpenses';
import { useLanguage } from '@/hooks/useLanguage';
import * as XLSX from 'xlsx';

export const ExportButtons = () => {
  const { allExpenses, summary } = useExpenses();
  const { language } = useLanguage();

  const translations = {
    en: {
      title: "Export & Print",
      description: "Export your expense data or print reports",
      printReport: "Print Report",
      exportExcel: "Export to Excel",
      downloadData: "Download Data",
      noData: "No expenses to export",
      addExpenses: "Add some expenses first"
    },
    ar: {
      title: "تصدير وطباعة",
      description: "تصدير بيانات المصروفات أو طباعة التقارير",
      printReport: "طباعة التقرير",
      exportExcel: "تصدير إلى Excel",
      downloadData: "تحميل البيانات",
      noData: "لا توجد مصروفات للتصدير",
      addExpenses: "أضف بعض المصروفات أولاً"
    }
  };

  const t = translations[language];

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Expense Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
            .summary-item { text-align: center; }
            .summary-value { font-size: 24px; font-weight: bold; color: #22c55e; }
            .expenses-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            .expenses-table th, .expenses-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .expenses-table th { background-color: #f2f2f2; }
            .total-row { font-weight: bold; background-color: #f9f9f9; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>💰 Personal Expense Report</h1>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
          </div>
          
          <div class="summary">
            <h2>Summary</h2>
            <div class="summary-grid">
              <div class="summary-item">
                <div>Daily Spending</div>
                <div class="summary-value">$${summary.daily.toFixed(2)}</div>
              </div>
              <div class="summary-item">
                <div>Weekly Spending</div>
                <div class="summary-value">$${summary.weekly.toFixed(2)}</div>
              </div>
              <div class="summary-item">
                <div>Monthly Spending</div>
                <div class="summary-value">$${summary.monthly.toFixed(2)}</div>
              </div>
              <div class="summary-item">
                <div>Total Spending</div>
                <div class="summary-value">$${summary.total.toFixed(2)}</div>
              </div>
            </div>
          </div>

          <h2>Expenses</h2>
          <table class="expenses-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Category</th>
                <th>Amount</th>
                <th>Note</th>
              </tr>
            </thead>
            <tbody>
              ${allExpenses.map(expense => `
                <tr>
                  <td>${new Date(expense.date).toLocaleDateString()}</td>
                  <td>${expense.category.emoji} ${expense.category.name}</td>
                  <td>$${expense.amount.toFixed(2)}</td>
                  <td>${expense.note || '-'}</td>
                </tr>
              `).join('')}
              <tr class="total-row">
                <td colspan="2"><strong>Total</strong></td>
                <td><strong>$${summary.total.toFixed(2)}</strong></td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const handleExportExcel = () => {
    if (allExpenses.length === 0) return;

    // Prepare data for Excel
    const excelData = allExpenses.map(expense => ({
      Date: new Date(expense.date).toLocaleDateString(),
      Category: expense.category.name,
      Emoji: expense.category.emoji,
      Amount: expense.amount,
      Note: expense.note || '',
      'Created At': new Date(expense.createdAt).toLocaleDateString()
    }));

    // Add summary row
    excelData.push({
      Date: '',
      Category: 'TOTAL',
      Emoji: '',
      Amount: summary.total,
      Note: `${allExpenses.length} transactions`,
      'Created At': ''
    });

    // Create workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData);

    // Auto-adjust column widths
    const colWidths = [
      { wch: 12 }, // Date
      { wch: 20 }, // Category  
      { wch: 5 },  // Emoji
      { wch: 10 }, // Amount
      { wch: 30 }, // Note
      { wch: 12 }  // Created At
    ];
    ws['!cols'] = colWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Expenses');

    // Generate filename with current date
    const filename = `expense-report-${new Date().toISOString().split('T')[0]}.xlsx`;
    
    // Download file
    XLSX.writeFile(wb, filename);
  };

  const handleDownloadJSON = () => {
    if (allExpenses.length === 0) return;

    const data = {
      exportDate: new Date().toISOString(),
      summary,
      expenses: allExpenses
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `expense-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (allExpenses.length === 0) {
    return (
      <Card className="p-8 text-center bg-gradient-card border-0 shadow-card">
        <FileSpreadsheet className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-xl font-semibold mb-2">{t.noData}</h3>
        <p className="text-muted-foreground">{t.addExpenses}</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">{t.title}</h2>
        <p className="text-muted-foreground">{t.description}</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="p-6 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
          <div className="text-center space-y-4">
            <div className="p-4 bg-primary/10 rounded-full w-fit mx-auto">
              <Printer className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold mb-2">{t.printReport}</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Print a formatted expense report
              </p>
              <Button onClick={handlePrint} className="w-full bg-gradient-primary">
                <Printer className="h-4 w-4 mr-2" />
                {t.printReport}
              </Button>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
          <div className="text-center space-y-4">
            <div className="p-4 bg-primary/10 rounded-full w-fit mx-auto">
              <FileSpreadsheet className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold mb-2">{t.exportExcel}</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Export data to Excel spreadsheet
              </p>
              <Button onClick={handleExportExcel} className="w-full bg-gradient-primary">
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                {t.exportExcel}
              </Button>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
          <div className="text-center space-y-4">
            <div className="p-4 bg-primary/10 rounded-full w-fit mx-auto">
              <Download className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold mb-2">{t.downloadData}</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Download raw data as JSON
              </p>
              <Button onClick={handleDownloadJSON} className="w-full bg-gradient-primary">
                <Download className="h-4 w-4 mr-2" />
                {t.downloadData}
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Stats */}
      <Card className="p-6 bg-gradient-card border-0 shadow-card">
        <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-primary">{allExpenses.length}</p>
            <p className="text-sm text-muted-foreground">Total Expenses</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-primary">${summary.total.toFixed(2)}</p>
            <p className="text-sm text-muted-foreground">Total Amount</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-primary">
              ${allExpenses.length > 0 ? (summary.total / allExpenses.length).toFixed(2) : '0.00'}
            </p>
            <p className="text-sm text-muted-foreground">Average</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-primary">
              {Object.keys(summary.categoryBreakdown).length}
            </p>
            <p className="text-sm text-muted-foreground">Categories Used</p>
          </div>
        </div>
      </Card>
    </div>
  );
};