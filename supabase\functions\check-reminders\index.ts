import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.51.0';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req) => {
  console.log('Check reminders function called');

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get all users with notification preferences
    const { data: preferences, error: prefsError } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('browser_notifications_enabled', true);

    if (prefsError) {
      console.error('Error fetching preferences:', prefsError);
      throw prefsError;
    }

    console.log(`Found ${preferences?.length || 0} users with notifications enabled`);

    let remindersSent = 0;

    for (const pref of preferences || []) {
      try {
        // Check for expense reminders
        if (pref.expense_reminder_enabled) {
          const lastExpenseTime = pref.last_expense_logged_at 
            ? new Date(pref.last_expense_logged_at)
            : new Date(0);
          
          const hoursSinceLastExpense = (Date.now() - lastExpenseTime.getTime()) / (1000 * 60 * 60);
          
          if (hoursSinceLastExpense >= pref.expense_reminder_hours) {
            console.log(`User ${pref.user_id} needs expense reminder`);
            remindersSent++;
          }
        }

        // Check for recurring bill reminders
        if (pref.recurring_bill_reminder_enabled) {
          const reminderDate = new Date();
          reminderDate.setDate(reminderDate.getDate() + pref.recurring_bill_reminder_days);
          
          const { data: upcomingBills, error: billsError } = await supabase
            .from('recurring_expenses')
            .select('*')
            .eq('user_id', pref.user_id)
            .eq('is_active', true)
            .lte('next_due_date', reminderDate.toISOString().split('T')[0]);

          if (billsError) {
            console.error('Error fetching recurring expenses:', billsError);
            continue;
          }

          if (upcomingBills && upcomingBills.length > 0) {
            console.log(`User ${pref.user_id} has ${upcomingBills.length} upcoming bills`);
            remindersSent++;
          }
        }
      } catch (userError) {
        console.error(`Error processing reminders for user ${pref.user_id}:`, userError);
      }
    }

    console.log(`Check reminders completed. ${remindersSent} reminders identified.`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        remindersSent,
        usersChecked: preferences?.length || 0 
      }),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );

  } catch (error) {
    console.error('Error in check-reminders function:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
});