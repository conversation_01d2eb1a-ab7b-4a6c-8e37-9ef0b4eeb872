import { useMemo } from 'react';
import { TrendingUp, TrendingDown, DollarSign, PieChart } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { useExpenses } from '@/hooks/useExpenses';
import { useLanguage } from '@/hooks/useLanguage';

export const Dashboard = () => {
  const { summary, categories, allExpenses } = useExpenses();
  const { language } = useLanguage();

  const translations = {
    en: {
      overview: "Spending Overview",
      totalSpent: "Total Spent",
      topCategories: "Top Categories",
      recentTrend: "Recent Trend",
      noExpenses: "No expenses yet",
      addFirst: "Add your first expense to see insights"
    },
    ar: {
      overview: "نظرة عامة على المصروفات",
      totalSpent: "إجمالي المصروفات",
      topCategories: "أهم الفئات",
      recentTrend: "الاتجاه الأخير",
      noExpenses: "لا توجد مصروفات بعد",
      addFirst: "أضف مصروفك الأول لرؤية الإحصائيات"
    }
  };

  const t = translations[language];

  // Calculate top categories
  const topCategories = useMemo(() => {
    return Object.entries(summary.categoryBreakdown)
      .map(([categoryId, amount]) => {
        const category = categories.find(c => c.id === categoryId);
        return { category, amount };
      })
      .filter(item => item.category)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 3);
  }, [summary.categoryBreakdown, categories]);

  // Calculate trend (comparing last 7 days vs previous 7 days)
  const trend = useMemo(() => {
    const now = new Date();
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const previous7Days = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const lastWeekSpending = allExpenses
      .filter(e => e.date >= last7Days)
      .reduce((sum, e) => sum + e.amount, 0);

    const previousWeekSpending = allExpenses
      .filter(e => e.date >= previous7Days && e.date < last7Days)
      .reduce((sum, e) => sum + e.amount, 0);

    const change = previousWeekSpending === 0 ? 0 : 
      ((lastWeekSpending - previousWeekSpending) / previousWeekSpending) * 100;

    return { current: lastWeekSpending, previous: previousWeekSpending, change };
  }, [allExpenses]);

  if (allExpenses.length === 0) {
    return (
      <Card className="p-8 text-center bg-gradient-card border-0 shadow-card">
        <PieChart className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-xl font-semibold mb-2">{t.noExpenses}</h3>
        <p className="text-muted-foreground">{t.addFirst}</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <DollarSign className="h-6 w-6 text-primary" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t.totalSpent}</p>
              <p className="text-3xl font-bold text-primary">${summary.total.toFixed(2)}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-lg ${trend.change >= 0 ? 'bg-destructive/10' : 'bg-primary/10'}`}>
              {trend.change >= 0 ? (
                <TrendingUp className={`h-6 w-6 ${trend.change >= 0 ? 'text-destructive' : 'text-primary'}`} />
              ) : (
                <TrendingDown className="h-6 w-6 text-primary" />
              )}
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t.recentTrend}</p>
              <p className={`text-2xl font-bold ${trend.change >= 0 ? 'text-destructive' : 'text-primary'}`}>
                {trend.change >= 0 ? '+' : ''}{trend.change.toFixed(1)}%
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Top Categories */}
      <Card className="p-6 bg-gradient-card border-0 shadow-card">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <PieChart className="h-5 w-5" />
          {t.topCategories}
        </h3>
        
        <div className="space-y-4">
          {topCategories.map(({ category, amount }) => {
            if (!category) return null;
            
            const percentage = (amount / summary.total) * 100;
            
            return (
              <div key={category.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{category.emoji}</span>
                    <div>
                      <p className="font-medium">{category.name}</p>
                      <p className="text-sm text-muted-foreground">{percentage.toFixed(1)}%</p>
                    </div>
                  </div>
                  <p className="font-bold text-lg">${amount.toFixed(2)}</p>
                </div>
                
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="h-2 bg-primary rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Quick Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 bg-gradient-card border-0 shadow-card text-center">
          <p className="text-2xl font-bold text-primary">{allExpenses.length}</p>
          <p className="text-sm text-muted-foreground">Total Transactions</p>
        </Card>
        
        <Card className="p-4 bg-gradient-card border-0 shadow-card text-center">
          <p className="text-2xl font-bold text-primary">
            ${allExpenses.length > 0 ? (summary.total / allExpenses.length).toFixed(2) : '0.00'}
          </p>
          <p className="text-sm text-muted-foreground">Avg per Transaction</p>
        </Card>
        
        <Card className="p-4 bg-gradient-card border-0 shadow-card text-center">
          <p className="text-2xl font-bold text-primary">
            ${(summary.monthly / 30).toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground">Daily Average</p>
        </Card>
      </div>
    </div>
  );
};