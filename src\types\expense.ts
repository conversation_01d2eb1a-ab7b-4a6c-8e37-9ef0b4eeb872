export interface Expense {
  id: string;
  amount: number;
  category: ExpenseCategory;
  note: string;
  date: string;
  invoice?: File | null;
  invoiceUrl?: string;
  createdAt: string;
}

export interface ExpenseCategory {
  id: string;
  name: string;
  emoji: string;
  color: string;
}

export const DEFAULT_CATEGORIES: ExpenseCategory[] = [
  { id: 'food', name: 'Food & Dining', emoji: '🍔', color: 'expense-food' },
  { id: 'coffee', name: 'Coffee & Drinks', emoji: '☕', color: 'expense-food' },
  { id: 'transport', name: 'Transport', emoji: '⛽', color: 'expense-transport' },
  { id: 'shopping', name: 'Shopping', emoji: '🛍️', color: 'expense-shopping' },
  { id: 'health', name: 'Health & Medical', emoji: '🏥', color: 'expense-health' },
  { id: 'entertainment', name: 'Entertainment', emoji: '🎬', color: 'expense-entertainment' },
  { id: 'misc', name: 'Miscellaneous', emoji: '🦋', color: 'expense-misc' },
];

export interface ExpenseFilters {
  category?: string;
  startDate?: string;
  endDate?: string;
  searchTerm?: string;
}

export interface ExpenseSummary {
  daily: number;
  weekly: number;
  monthly: number;
  total: number;
  categoryBreakdown: { [key: string]: number };
}

export interface RecurringExpense {
  id: string;
  amount: number;
  category: ExpenseCategory;
  note: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  frequencyInterval: number;
  startDate: string;
  nextDueDate: string;
  endDate?: string;
  isActive: boolean;
  createdAt: string;
}