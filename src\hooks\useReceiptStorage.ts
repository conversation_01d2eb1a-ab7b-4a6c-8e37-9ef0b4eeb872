import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/AuthProvider';
import { toast } from '@/hooks/use-toast';

export interface ReceiptFile {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  expenseId?: string;
  createdAt: string;
}

export const useReceiptStorage = () => {
  const [uploading, setUploading] = useState(false);
  const [receipts, setReceipts] = useState<ReceiptFile[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  const uploadReceipt = async (file: File, expenseId?: string): Promise<string | null> => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to upload receipts",
        variant: "destructive"
      });
      return null;
    }

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('receipts')
        .upload(fileName, file);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('receipts')
        .getPublicUrl(fileName);

      // Update expense with receipt URL if expenseId provided
      if (expenseId) {
        const { error: updateError } = await supabase
          .from('expenses')
          .update({ file_url: publicUrl })
          .eq('id', expenseId)
          .eq('user_id', user.id);

        if (updateError) throw updateError;
      }

      toast({
        title: "Receipt uploaded",
        description: "Your receipt has been saved successfully"
      });

      return publicUrl;
    } catch (error: any) {
      console.error('Error uploading receipt:', error);
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive"
      });
      return null;
    } finally {
      setUploading(false);
    }
  };

  const deleteReceipt = async (fileName: string, expenseId?: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const { error } = await supabase.storage
        .from('receipts')
        .remove([fileName]);

      if (error) throw error;

      // Remove receipt URL from expense if provided
      if (expenseId) {
        const { error: updateError } = await supabase
          .from('expenses')
          .update({ file_url: null })
          .eq('id', expenseId)
          .eq('user_id', user.id);

        if (updateError) throw updateError;
      }

      toast({
        title: "Receipt deleted",
        description: "Receipt has been removed successfully"
      });

      return true;
    } catch (error: any) {
      console.error('Error deleting receipt:', error);
      toast({
        title: "Delete failed",
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  };

  const fetchUserReceipts = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase.storage
        .from('receipts')
        .list(`${user.id}/`, {
          limit: 100,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) throw error;

      const receiptFiles: ReceiptFile[] = data.map(file => ({
        id: file.id || file.name,
        name: file.name,
        url: supabase.storage.from('receipts').getPublicUrl(`${user.id}/${file.name}`).data.publicUrl,
        size: file.metadata?.size || 0,
        type: file.metadata?.mimetype || 'unknown',
        createdAt: file.created_at || new Date().toISOString()
      }));

      setReceipts(receiptFiles);
    } catch (error: any) {
      console.error('Error fetching receipts:', error);
      toast({
        title: "Failed to load receipts",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    uploadReceipt,
    deleteReceipt,
    fetchUserReceipts,
    receipts,
    uploading,
    loading
  };
};