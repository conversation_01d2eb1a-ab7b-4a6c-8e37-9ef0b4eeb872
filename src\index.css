@tailwind base;
@tailwind components;
@tailwind utilities;

/* Personal Expense Tracker Design System - Modern, Minimal, Personal */

@layer base {
  :root {
    /* Beautiful gradient background */
    --background: 240 10% 98%;
    --foreground: 240 10% 15%;

    /* Soft card system */
    --card: 0 0% 100%;
    --card-foreground: 240 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 15%;

    /* Modern green primary (friendly money color) */
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 100%;
    --primary-soft: 142 40% 95%;

    /* Soft secondary system */
    --secondary: 240 5% 96%;
    --secondary-foreground: 240 10% 15%;

    /* Muted tones */
    --muted: 240 5% 96%;
    --muted-foreground: 240 5% 45%;

    /* Accent colors */
    --accent: 240 5% 96%;
    --accent-foreground: 240 10% 15%;

    /* Friendly red for expenses */
    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Subtle borders */
    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 142 76% 36%;

    /* Category colors for expense types */
    --expense-food: 25 85% 60%;
    --expense-transport: 210 85% 60%;
    --expense-shopping: 290 85% 60%;
    --expense-health: 340 85% 60%;
    --expense-entertainment: 45 85% 60%;
    --expense-misc: 180 85% 60%;

    /* Gradients for beautiful effects */
    --gradient-primary: linear-gradient(135deg, hsl(142 76% 36%), hsl(142 76% 50%));
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100%), hsl(240 5% 98%));
    --gradient-expense: linear-gradient(135deg, hsl(0 75% 60%), hsl(0 75% 70%));

    /* Shadows for depth */
    --shadow-soft: 0 2px 10px -2px hsl(240 10% 15% / 0.1);
    --shadow-card: 0 4px 20px -4px hsl(240 10% 15% / 0.15);
    --shadow-floating: 0 8px 30px -8px hsl(240 10% 15% / 0.2);

    /* Animation timings */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    transition: var(--transition-smooth);
  }

  /* RTL Support */
  .rtl {
    direction: rtl;
  }

  .rtl .animate-slide-up {
    animation-direction: reverse;
  }

  /* Smooth transitions for all interactive elements */
  button, input, textarea, select {
    transition: var(--transition-smooth);
  }

  /* Enhanced focus styles */
  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary));
  }
}