import { useState, useRef } from 'react';
import { Camera, Upload, Image, FileText, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useReceiptStorage } from '@/hooks/useReceiptStorage';
import { useLanguage } from '@/hooks/useLanguage';

interface ReceiptUploadProps {
  expenseId?: string;
  existingReceiptUrl?: string;
  onReceiptUploaded?: (url: string) => void;
  onReceiptRemoved?: () => void;
}

export const ReceiptUpload = ({ 
  expenseId, 
  existingReceiptUrl, 
  onReceiptUploaded,
  onReceiptRemoved 
}: ReceiptUploadProps) => {
  const [preview, setPreview] = useState<string | null>(existingReceiptUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const { uploadReceipt, deleteReceipt, uploading } = useReceiptStorage();
  const { language } = useLanguage();

  const translations = {
    en: {
      uploadReceipt: "Upload Receipt",
      takePhoto: "Take Photo",
      chooseFile: "Choose File",
      dragDrop: "Drag & drop a receipt here",
      or: "or",
      removeReceipt: "Remove Receipt",
      uploading: "Uploading...",
      supportedFormats: "JPG, PNG, PDF up to 10MB"
    },
    ar: {
      uploadReceipt: "رفع الإيصال",
      takePhoto: "التقاط صورة",
      chooseFile: "اختر ملف",
      dragDrop: "اسحب وأفلت الإيصال هنا",
      or: "أو",
      removeReceipt: "إزالة الإيصال",
      uploading: "جاري الرفع...",
      supportedFormats: "JPG, PNG, PDF حتى 10 ميجا"
    }
  };

  const t = translations[language];

  const handleFileSelect = async (file: File) => {
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      alert('Only JPG, PNG, GIF, and PDF files are allowed');
      return;
    }

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target?.result as string);
      reader.readAsDataURL(file);
    } else {
      setPreview('pdf');
    }

    const url = await uploadReceipt(file, expenseId);
    if (url && onReceiptUploaded) {
      onReceiptUploaded(url);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemoveReceipt = async () => {
    if (existingReceiptUrl && expenseId) {
      const fileName = existingReceiptUrl.split('/').pop();
      if (fileName) {
        const success = await deleteReceipt(fileName, expenseId);
        if (success) {
          setPreview(null);
          onReceiptRemoved?.();
        }
      }
    } else {
      setPreview(null);
      onReceiptRemoved?.();
    }
  };

  if (preview) {
    return (
      <Card className="p-4 bg-gradient-card border-0 shadow-card">
        <div className="flex items-center justify-between mb-3">
          <Badge variant="secondary" className="gap-1">
            {preview === 'pdf' ? <FileText className="h-3 w-3" /> : <Image className="h-3 w-3" />}
            Receipt Attached
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemoveReceipt}
            className="gap-1"
          >
            <X className="h-3 w-3" />
            {t.removeReceipt}
          </Button>
        </div>
        
        {preview !== 'pdf' && (
          <div className="rounded-lg overflow-hidden">
            <img 
              src={preview} 
              alt="Receipt preview" 
              className="w-full h-32 object-cover"
            />
          </div>
        )}
        
        {preview === 'pdf' && (
          <div className="flex items-center justify-center h-32 bg-muted rounded-lg">
            <FileText className="h-8 w-8 text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">PDF Receipt</span>
          </div>
        )}
      </Card>
    );
  }

  return (
    <Card 
      className="p-6 border-2 border-dashed border-border hover:border-primary/50 transition-all duration-300 bg-gradient-card"
      onDrop={handleDrop}
      onDragOver={(e) => e.preventDefault()}
    >
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="p-3 bg-primary/10 rounded-full">
            <Upload className="h-6 w-6 text-primary" />
          </div>
        </div>
        
        <div>
          <p className="text-sm font-medium">{t.dragDrop}</p>
          <p className="text-xs text-muted-foreground mt-1">{t.or}</p>
        </div>

        <div className="flex gap-2 justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => cameraInputRef.current?.click()}
            disabled={uploading}
            className="gap-2"
          >
            <Camera className="h-4 w-4" />
            {uploading ? t.uploading : t.takePhoto}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="gap-2"
          >
            <Upload className="h-4 w-4" />
            {uploading ? t.uploading : t.chooseFile}
          </Button>
        </div>

        <p className="text-xs text-muted-foreground">{t.supportedFormats}</p>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept=".jpg,.jpeg,.png,.gif,.pdf"
        onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
        className="hidden"
      />
      
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
        className="hidden"
      />
    </Card>
  );
};