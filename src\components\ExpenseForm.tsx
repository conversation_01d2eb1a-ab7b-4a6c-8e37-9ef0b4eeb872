import { useState } from 'react';
import { X, Upload, Calendar, Repeat } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useExpenses } from '@/hooks/useExpenses';
import { useRecurringExpenses } from '@/hooks/useRecurringExpenses';
import { useLanguage } from '@/hooks/useLanguage';
import { ExpenseCategory } from '@/types/expense';
import { ReceiptUpload } from './ReceiptUpload';

interface ExpenseFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ExpenseForm = ({ isOpen, onClose }: ExpenseFormProps) => {
  const { addExpense, categories } = useExpenses();
  const { addRecurringExpense } = useRecurringExpenses();
  const { language } = useLanguage();
  
  const [amount, setAmount] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ExpenseCategory | null>(null);
  const [note, setNote] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [receiptUrl, setReceiptUrl] = useState<string | undefined>(undefined);
  const [isRecurring, setIsRecurring] = useState(false);
  const [frequency, setFrequency] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');
  const [frequencyInterval, setFrequencyInterval] = useState(1);

  const translations = {
    en: {
      title: "Add New Expense",
      amount: "Amount ($)",
      category: "Category",
      note: "Note",
      date: "Date",
      receipt: "Receipt (optional)",
      makeRecurring: "Make this recurring",
      frequency: "Frequency",
      interval: "Every",
      daily: "Daily",
      weekly: "Weekly",
      monthly: "Monthly", 
      yearly: "Yearly",
      save: "Save Expense",
      cancel: "Cancel",
      required: "Required",
      selectCategory: "Select a category"
    },
    ar: {
      title: "إضافة مصروف جديد",
      amount: "المبلغ ($)",
      category: "الفئة",
      note: "ملاحظة",
      date: "التاريخ",
      receipt: "الإيصال (اختياري)",
      makeRecurring: "جعل هذا متكرر",
      frequency: "التكرار",
      interval: "كل",
      daily: "يومي",
      weekly: "أسبوعي", 
      monthly: "شهري",
      yearly: "سنوي",
      save: "حفظ المصروف",
      cancel: "إلغاء",
      required: "مطلوب",
      selectCategory: "اختر فئة"
    }
  };

  const t = translations[language];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || !selectedCategory) return;

    if (isRecurring) {
      // Create recurring expense
      await addRecurringExpense({
        amount: parseFloat(amount),
        category: selectedCategory,
        note,
        frequency,
        frequencyInterval,
        startDate: date
      });
    } else {
      // Create one-time expense
      await addExpense({
        amount: parseFloat(amount),
        category: selectedCategory,
        note,
        date,
        invoiceUrl: receiptUrl,
      });
    }

    // Reset form
    setAmount('');
    setSelectedCategory(null);
    setNote('');
    setDate(new Date().toISOString().split('T')[0]);
    setReceiptUrl(undefined);
    setIsRecurring(false);
    setFrequency('monthly');
    setFrequencyInterval(1);
    
    onClose();
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-card/95 backdrop-blur-md border-0 shadow-floating animate-fade-in">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">{t.title}</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Amount */}
            <div>
              <Label htmlFor="amount">{t.amount} <span className="text-destructive">*</span></Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className="text-lg font-medium"
                required
              />
            </div>

            {/* Category */}
            <div>
              <Label>{t.category} <span className="text-destructive">*</span></Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    type="button"
                    variant={selectedCategory?.id === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category)}
                    className="h-auto p-3 flex flex-col gap-1 transition-all hover:scale-105"
                  >
                    <span className="text-lg">{category.emoji}</span>
                    <span className="text-xs text-center">{category.name}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Note */}
            <div>
              <Label htmlFor="note">{t.note}</Label>
              <Textarea
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                placeholder="What did you spend on?"
                className="resize-none"
                rows={2}
              />
            </div>

            {/* Date */}
            <div>
              <Label htmlFor="date">{t.date}</Label>
              <div className="relative">
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  className="pl-10"
                />
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            {/* Recurring Expense Toggle */}
            <div className="flex items-center space-x-2 p-3 border rounded-lg bg-muted/30">
              <Switch
                checked={isRecurring}
                onCheckedChange={setIsRecurring}
              />
              <div className="flex items-center gap-2">
                <Repeat className="h-4 w-4 text-primary" />
                <Label>{t.makeRecurring}</Label>
              </div>
            </div>

            {/* Recurring Frequency (only show if recurring is enabled) */}
            {isRecurring && (
              <div className="grid grid-cols-2 gap-4 p-3 border rounded-lg bg-primary/5">
                <div>
                  <Label>{t.interval}</Label>
                  <Input
                    type="number"
                    min="1"
                    value={frequencyInterval}
                    onChange={(e) => setFrequencyInterval(parseInt(e.target.value) || 1)}
                  />
                </div>
                <div>
                  <Label>{t.frequency}</Label>
                  <Select value={frequency} onValueChange={(value: any) => setFrequency(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">{t.daily}</SelectItem>
                      <SelectItem value="weekly">{t.weekly}</SelectItem>
                      <SelectItem value="monthly">{t.monthly}</SelectItem>
                      <SelectItem value="yearly">{t.yearly}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Receipt Upload (only for one-time expenses) */}
            {!isRecurring && (
              <div>
                <Label>{t.receipt}</Label>
                <div className="mt-2">
                  <ReceiptUpload
                    existingReceiptUrl={receiptUrl}
                    onReceiptUploaded={setReceiptUrl}
                    onReceiptRemoved={() => setReceiptUrl(undefined)}
                  />
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                {t.cancel}
              </Button>
              <Button 
                type="submit" 
                className="flex-1 bg-gradient-primary"
                disabled={!amount || !selectedCategory}
              >
                {t.save}
              </Button>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
};