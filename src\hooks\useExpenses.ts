import { useState, useEffect, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Expense, ExpenseFilters, ExpenseSummary, DEFAULT_CATEGORIES } from '@/types/expense';
import { useToast } from '@/hooks/use-toast';

export const useExpenses = () => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [filters, setFilters] = useState<ExpenseFilters>({});
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Load expenses from Supabase on mount
  useEffect(() => {
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform Supabase data to match local Expense type
      const transformedExpenses: Expense[] = (data || []).map(expense => {
        // Find the category from DEFAULT_CATEGORIES to get the color
        const category = DEFAULT_CATEGORIES.find(cat => cat.id === expense.category_id);
        return {
          id: expense.id,
          amount: parseFloat(expense.amount.toString()),
          category: {
            id: expense.category_id,
            name: expense.category_name,
            emoji: expense.category_emoji,
            color: category?.color || '#6B7280' // fallback color
          },
          note: expense.note,
          date: expense.date,
          invoiceUrl: expense.file_url,
          createdAt: expense.created_at
        };
      });

      setExpenses(transformedExpenses);
    } catch (error) {
      console.error('Failed to fetch expenses:', error);
      toast({
        title: "Error",
        description: "Failed to load expenses. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const addExpense = async (expenseData: Omit<Expense, 'id' | 'createdAt'>) => {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .insert({
          user_id: (await supabase.auth.getUser()).data.user?.id,
          amount: expenseData.amount,
          category_id: expenseData.category.id,
          category_name: expenseData.category.name,
          category_emoji: expenseData.category.emoji,
          note: expenseData.note,
          date: expenseData.date,
          file_url: expenseData.invoiceUrl
        })
        .select()
        .single();

      if (error) throw error;

      // Transform and add to local state
      const category = DEFAULT_CATEGORIES.find(cat => cat.id === data.category_id);
      const newExpense: Expense = {
        id: data.id,
        amount: parseFloat(data.amount.toString()),
        category: {
          id: data.category_id,
          name: data.category_name,
          emoji: data.category_emoji,
          color: category?.color || '#6B7280'
        },
          note: data.note,
          date: data.date,
          invoiceUrl: data.file_url,
          createdAt: data.created_at
      };

      setExpenses(prev => [newExpense, ...prev]);
      
      toast({
        title: "Success",
        description: "Expense added successfully!",
      });
    } catch (error) {
      console.error('Failed to add expense:', error);
      toast({
        title: "Error",
        description: "Failed to add expense. Please try again.",
        variant: "destructive"
      });
    }
  };

  const updateExpense = async (id: string, updates: Partial<Expense>) => {
    try {
      const updateData: any = {};
      if (updates.amount !== undefined) updateData.amount = updates.amount;
      if (updates.category) {
        updateData.category_id = updates.category.id;
        updateData.category_name = updates.category.name;
        updateData.category_emoji = updates.category.emoji;
      }
      if (updates.note !== undefined) updateData.note = updates.note;
      if (updates.date !== undefined) updateData.date = updates.date;
      if (updates.invoiceUrl !== undefined) updateData.file_url = updates.invoiceUrl;

      const { error } = await supabase
        .from('expenses')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setExpenses(prev => 
        prev.map(expense => 
          expense.id === id ? { ...expense, ...updates } : expense
        )
      );

      toast({
        title: "Success",
        description: "Expense updated successfully!",
      });
    } catch (error) {
      console.error('Failed to update expense:', error);
      toast({
        title: "Error",
        description: "Failed to update expense. Please try again.",
        variant: "destructive"
      });
    }
  };

  const deleteExpense = async (id: string) => {
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setExpenses(prev => prev.filter(expense => expense.id !== id));
      
      toast({
        title: "Success",
        description: "Expense deleted successfully!",
      });
    } catch (error) {
      console.error('Failed to delete expense:', error);
      toast({
        title: "Error",
        description: "Failed to delete expense. Please try again.",
        variant: "destructive"
      });
    }
  };

  const clearAllExpenses = async () => {
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

      if (error) throw error;

      setExpenses([]);
      
      toast({
        title: "Success",
        description: "All expenses cleared successfully!",
      });
    } catch (error) {
      console.error('Failed to clear expenses:', error);
      toast({
        title: "Error",
        description: "Failed to clear expenses. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Filtered expenses based on current filters
  const filteredExpenses = useMemo(() => {
    return expenses.filter(expense => {
      if (filters.category && expense.category.id !== filters.category) {
        return false;
      }
      
      if (filters.startDate && expense.date < filters.startDate) {
        return false;
      }
      
      if (filters.endDate && expense.date > filters.endDate) {
        return false;
      }
      
      if (filters.searchTerm) {
        const search = filters.searchTerm.toLowerCase();
        return (
          expense.note.toLowerCase().includes(search) ||
          expense.category.name.toLowerCase().includes(search)
        );
      }
      
      return true;
    });
  }, [expenses, filters]);

  // Calculate summary statistics
  const summary: ExpenseSummary = useMemo(() => {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const daily = expenses
      .filter(e => e.date === today)
      .reduce((sum, e) => sum + e.amount, 0);

    const weekly = expenses
      .filter(e => e.date >= weekAgo)
      .reduce((sum, e) => sum + e.amount, 0);

    const monthly = expenses
      .filter(e => e.date >= monthAgo)
      .reduce((sum, e) => sum + e.amount, 0);

    const total = expenses.reduce((sum, e) => sum + e.amount, 0);

    const categoryBreakdown = expenses.reduce((acc, expense) => {
      const categoryId = expense.category.id;
      acc[categoryId] = (acc[categoryId] || 0) + expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    return { daily, weekly, monthly, total, categoryBreakdown };
  }, [expenses]);

  return {
    expenses: filteredExpenses,
    allExpenses: expenses,
    summary,
    filters,
    setFilters,
    addExpense,
    updateExpense,
    deleteExpense,
    clearAllExpenses,
    categories: DEFAULT_CATEGORIES,
    loading,
  };
};