import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PiggyBank, Save, X } from 'lucide-react';
import { useBudgets } from '@/hooks/useBudgets';
import { useLanguage } from '@/hooks/useLanguage';
import { DEFAULT_CATEGORIES } from '@/types/expense';

interface BudgetFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export const BudgetForm = ({ isOpen, onClose }: BudgetFormProps) => {
  const [amount, setAmount] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [period, setPeriod] = useState<'weekly' | 'monthly' | 'yearly'>('monthly');
  const [submitting, setSubmitting] = useState(false);

  const { addBudget } = useBudgets();
  const { language, isRTL } = useLanguage();

  const translations = {
    en: {
      title: "Create Budget",
      amount: "Budget Amount",
      category: "Category",
      period: "Period",
      weekly: "Weekly",
      monthly: "Monthly",
      yearly: "Yearly",
      save: "Create Budget",
      cancel: "Cancel",
      selectCategory: "Select a category",
      amountPlaceholder: "Enter budget amount"
    },
    ar: {
      title: "إنشاء ميزانية",
      amount: "مبلغ الميزانية",
      category: "الفئة",
      period: "الفترة",
      weekly: "أسبوعي",
      monthly: "شهري",
      yearly: "سنوي",
      save: "إنشاء ميزانية",
      cancel: "إلغاء",
      selectCategory: "اختر فئة",
      amountPlaceholder: "أدخل مبلغ الميزانية"
    }
  };

  const t = translations[language];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount || !categoryId) return;

    setSubmitting(true);
    
    const selectedCategory = DEFAULT_CATEGORIES.find(cat => cat.id === categoryId);
    if (!selectedCategory) return;

    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];

    await addBudget({
      amount: parseFloat(amount),
      category: selectedCategory,
      period,
      startDate,
      isActive: true
    });

    // Reset form
    setAmount('');
    setCategoryId('');
    setPeriod('monthly');
    setSubmitting(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`max-w-md bg-gradient-card border-0 shadow-floating ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            <PiggyBank className="h-5 w-5 text-primary" />
            {t.title}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="amount">{t.amount}</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder={t.amountPlaceholder}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">{t.category}</Label>
            <Select value={categoryId} onValueChange={setCategoryId} required>
              <SelectTrigger>
                <SelectValue placeholder={t.selectCategory} />
              </SelectTrigger>
              <SelectContent>
                {DEFAULT_CATEGORIES.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <span className="flex items-center gap-2">
                      <span>{category.emoji}</span>
                      <span>{category.name}</span>
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t.period}</Label>
            <Select value={period} onValueChange={(value: any) => setPeriod(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">{t.weekly}</SelectItem>
                <SelectItem value="monthly">{t.monthly}</SelectItem>
                <SelectItem value="yearly">{t.yearly}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={submitting}
            >
              <X className="mr-2 h-4 w-4" />
              {t.cancel}
            </Button>
            <Button
              type="submit"
              disabled={!amount || !categoryId || submitting}
              className="flex-1 bg-gradient-primary hover:opacity-90"
            >
              <Save className="mr-2 h-4 w-4" />
              {submitting ? "..." : t.save}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};