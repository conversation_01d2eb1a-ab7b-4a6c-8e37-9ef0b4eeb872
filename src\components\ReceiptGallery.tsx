import { useEffect, useState } from 'react';
import { Image, FileText, Trash2, Download, Eye } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useReceiptStorage } from '@/hooks/useReceiptStorage';
import { useLanguage } from '@/hooks/useLanguage';
import { format } from 'date-fns';

export const ReceiptGallery = () => {
  const [selectedReceipt, setSelectedReceipt] = useState<any>(null);
  const { receipts, loading, fetchUserReceipts, deleteReceipt } = useReceiptStorage();
  const { language } = useLanguage();

  const translations = {
    en: {
      title: "Receipt Gallery",
      description: "View and manage your uploaded receipts",
      noReceipts: "No receipts found",
      addReceipts: "Upload receipts with your expenses to see them here",
      viewReceipt: "View",
      downloadReceipt: "Download",
      deleteReceipt: "Delete",
      receiptDetails: "Receipt Details",
      fileName: "File Name",
      fileSize: "File Size",
      uploadDate: "Upload Date"
    },
    ar: {
      title: "معرض الإيصالات",
      description: "عرض وإدارة الإيصالات المرفوعة",
      noReceipts: "لا توجد إيصالات",
      addReceipts: "ارفع الإيصالات مع مصروفاتك لتراها هنا",
      viewReceipt: "عرض",
      downloadReceipt: "تحميل",
      deleteReceipt: "حذف",
      receiptDetails: "تفاصيل الإيصال",
      fileName: "اسم الملف",
      fileSize: "حجم الملف",
      uploadDate: "تاريخ الرفع"
    }
  };

  const t = translations[language];

  useEffect(() => {
    fetchUserReceipts();
  }, []);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = (receipt: any) => {
    const link = document.createElement('a');
    link.href = receipt.url;
    link.download = receipt.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDelete = async (receipt: any) => {
    if (confirm('Are you sure you want to delete this receipt?')) {
      const success = await deleteReceipt(receipt.name);
      if (success) {
        fetchUserReceipts(); // Refresh the list
      }
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">{t.title}</h2>
          <p className="text-muted-foreground">{t.description}</p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="p-4 bg-gradient-card border-0 shadow-card">
              <div className="animate-pulse">
                <div className="h-32 bg-muted rounded mb-3"></div>
                <div className="h-4 bg-muted rounded mb-2"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (receipts.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">{t.title}</h2>
          <p className="text-muted-foreground">{t.description}</p>
        </div>
        
        <Card className="p-8 text-center bg-gradient-card border-0 shadow-card">
          <Image className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-semibold mb-2">{t.noReceipts}</h3>
          <p className="text-muted-foreground">{t.addReceipts}</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">{t.title}</h2>
        <p className="text-muted-foreground">{t.description}</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {receipts.map((receipt) => (
          <Card key={receipt.id} className="p-4 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
            <div className="space-y-3">
              {/* Preview */}
              <div 
                className="h-32 rounded-lg overflow-hidden cursor-pointer bg-muted flex items-center justify-center"
                onClick={() => setSelectedReceipt(receipt)}
              >
                {receipt.type.startsWith('image/') ? (
                  <img 
                    src={receipt.url} 
                    alt={receipt.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <FileText className="h-8 w-8 text-muted-foreground" />
                )}
              </div>

              {/* File Info */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="gap-1 text-xs">
                    {receipt.type.startsWith('image/') ? 
                      <Image className="h-3 w-3" /> : 
                      <FileText className="h-3 w-3" />
                    }
                    {receipt.type.startsWith('image/') ? 'Image' : 'PDF'}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {formatFileSize(receipt.size)}
                  </span>
                </div>
                
                <p className="text-sm font-medium truncate" title={receipt.name}>
                  {receipt.name}
                </p>
                
                <p className="text-xs text-muted-foreground">
                  {format(new Date(receipt.createdAt), 'MMM dd, yyyy')}
                </p>
              </div>

              {/* Actions */}
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedReceipt(receipt)}
                  className="flex-1 gap-1"
                >
                  <Eye className="h-3 w-3" />
                  {t.viewReceipt}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownload(receipt)}
                  className="gap-1"
                >
                  <Download className="h-3 w-3" />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDelete(receipt)}
                  className="gap-1 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Receipt Detail Dialog */}
      <Dialog open={!!selectedReceipt} onOpenChange={() => setSelectedReceipt(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>{t.receiptDetails}</DialogTitle>
          </DialogHeader>
          
          {selectedReceipt && (
            <div className="space-y-6">
              {/* Receipt Preview */}
              <div className="flex justify-center">
                {selectedReceipt.type.startsWith('image/') ? (
                  <img 
                    src={selectedReceipt.url} 
                    alt={selectedReceipt.name}
                    className="max-h-96 rounded-lg shadow-md"
                  />
                ) : (
                  <div className="p-8 bg-muted rounded-lg text-center">
                    <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-lg font-medium">PDF Receipt</p>
                    <p className="text-sm text-muted-foreground">{selectedReceipt.name}</p>
                  </div>
                )}
              </div>

              {/* File Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t.fileName}</p>
                  <p className="text-sm">{selectedReceipt.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t.fileSize}</p>
                  <p className="text-sm">{formatFileSize(selectedReceipt.size)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t.uploadDate}</p>
                  <p className="text-sm">{format(new Date(selectedReceipt.createdAt), 'PPP')}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 justify-end">
                <Button
                  variant="outline"
                  onClick={() => handleDownload(selectedReceipt)}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  {t.downloadReceipt}
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    handleDelete(selectedReceipt);
                    setSelectedReceipt(null);
                  }}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  {t.deleteReceipt}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};