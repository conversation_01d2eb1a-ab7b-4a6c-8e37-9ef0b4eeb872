import { useState } from 'react';
import { Plus, BarChart3, History, Download, Languages, LogOut, Repeat, Settings, PiggyBank, Image } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useExpenses } from '@/hooks/useExpenses';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from './AuthProvider';
import { ExpenseForm } from './ExpenseForm';
import { Dashboard } from './Dashboard';
import { ExpenseHistory } from './ExpenseHistory';
import { ExportButtons } from './ExportButtons';
import { RecurringExpensesList } from './RecurringExpensesList';
import { NotificationSettings } from './NotificationSettings';
import { useNotificationPreferences } from '@/hooks/useNotificationPreferences';
import { BudgetsList } from './BudgetsList';
import { AnalyticsDashboard } from './AnalyticsDashboard';
import { ReceiptGallery } from './ReceiptGallery';

export const ExpenseTracker = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const { summary, loading } = useExpenses();
  const { language, toggleLanguage, isRTL } = useLanguage();
  const { user, signOut } = useAuth();

  const translations = {
    en: {
      title: "💰 Personal Expense Tracker",
      addExpense: "Add Expense",
      dashboard: "Dashboard",
      history: "History",
      recurring: "Recurring",
      budgets: "Budgets",
      analytics: "Analytics",
      receipts: "Receipts",
      settings: "Settings",
      export: "Export",
      todaySpent: "Today's Spending",
      weekSpent: "This Week",
      monthSpent: "This Month"
    },
    ar: {
      title: "💰 متتبع المصروفات الشخصية",
      addExpense: "إضافة مصروف",
      dashboard: "لوحة التحكم", 
      history: "السجل",
      recurring: "متكرر",
      budgets: "الميزانيات",
      analytics: "التحليلات",
      receipts: "الإيصالات",
      settings: "الإعدادات",
      export: "تصدير",
      todaySpent: "مصروفات اليوم",
      weekSpent: "هذا الأسبوع",
      monthSpent: "هذا الشهر"
    }
  };

  const t = translations[language];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-background to-primary-soft transition-all duration-500 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <header className="bg-card/80 backdrop-blur-md border-b border-border/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              {t.title}
            </h1>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleLanguage}
                className="gap-2"
              >
                <Languages className="h-4 w-4" />
                {language === 'en' ? 'العربية' : 'English'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={signOut}
                className="gap-2"
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
              
              <Button
                onClick={() => setShowExpenseForm(true)}
                className="bg-gradient-primary hover:opacity-90 transition-all gap-2 shadow-card"
              >
                <Plus className="h-4 w-4" />
                {t.addExpense}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Quick Stats */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="p-4 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">{t.todaySpent}</p>
              <p className="text-2xl font-bold text-primary">${summary.daily.toFixed(2)}</p>
            </div>
          </Card>
          
          <Card className="p-4 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">{t.weekSpent}</p>
              <p className="text-2xl font-bold text-primary">${summary.weekly.toFixed(2)}</p>
            </div>
          </Card>
          
          <Card className="p-4 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">{t.monthSpent}</p>
              <p className="text-2xl font-bold text-primary">${summary.monthly.toFixed(2)}</p>
            </div>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-8 bg-card/50 backdrop-blur-sm border border-border/50">
            <TabsTrigger value="dashboard" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              {t.dashboard}
            </TabsTrigger>
            <TabsTrigger value="history" className="gap-2">
              <History className="h-4 w-4" />
              {t.history}
            </TabsTrigger>
            <TabsTrigger value="recurring" className="gap-2">
              <Repeat className="h-4 w-4" />
              {t.recurring}
            </TabsTrigger>
            <TabsTrigger value="budgets" className="gap-2">
              <PiggyBank className="h-4 w-4" />
              {t.budgets}
            </TabsTrigger>
            <TabsTrigger value="analytics" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              {t.analytics}
            </TabsTrigger>
            <TabsTrigger value="receipts" className="gap-2">
              <Image className="h-4 w-4" />
              {t.receipts}
            </TabsTrigger>
            <TabsTrigger value="settings" className="gap-2">
              <Settings className="h-4 w-4" />
              {t.settings}
            </TabsTrigger>
            <TabsTrigger value="export" className="gap-2">
              <Download className="h-4 w-4" />
              {t.export}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="mt-6">
            <Dashboard />
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <ExpenseHistory />
          </TabsContent>

          <TabsContent value="recurring" className="mt-6">
            <RecurringExpensesList />
          </TabsContent>

          <TabsContent value="budgets" className="mt-6">
            <BudgetsList />
          </TabsContent>

          <TabsContent value="analytics" className="mt-6">
            <AnalyticsDashboard />
          </TabsContent>

          <TabsContent value="receipts" className="mt-6">
            <ReceiptGallery />
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <NotificationSettings translations={t} />
          </TabsContent>

          <TabsContent value="export" className="mt-6">
            <ExportButtons />
          </TabsContent>
        </Tabs>
      </div>

      {/* Expense Form Modal */}
      {showExpenseForm && (
        <ExpenseForm 
          isOpen={showExpenseForm}
          onClose={() => setShowExpenseForm(false)}
        />
      )}
    </div>
  );
};