import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { ExpenseCategory } from '@/types/expense';

export interface Budget {
  id: string;
  amount: number;
  category: ExpenseCategory;
  period: 'weekly' | 'monthly' | 'yearly';
  startDate: string;
  endDate?: string;
  isActive: boolean;
  createdAt: string;
}

interface BudgetWithSpending extends Budget {
  spent: number;
  remaining: number;
  percentage: number;
  isOverBudget: boolean;
}

export const useBudgets = () => {
  const [budgets, setBudgets] = useState<BudgetWithSpending[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchBudgets = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Fetch budgets
      const { data: budgetsData, error: budgetsError } = await supabase
        .from('budgets')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (budgetsError) throw budgetsError;

      // Fetch expenses to calculate spending
      const { data: expensesData, error: expensesError } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id);

      if (expensesError) throw expensesError;

      // Calculate spending for each budget
      const budgetsWithSpending: BudgetWithSpending[] = (budgetsData || []).map(budget => {
        const category = {
          id: budget.category_id,
          name: budget.category_name,
          emoji: budget.category_emoji,
          color: `expense-${budget.category_id}`
        };

        // Calculate date range based on period
        const now = new Date();
        let periodStart: Date;
        
        switch (budget.period) {
          case 'weekly':
            periodStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'yearly':
            periodStart = new Date(now.getFullYear(), 0, 1);
            break;
          case 'monthly':
          default:
            periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
        }

        // Calculate spending in the current period for this category
        const spent = expensesData
          ?.filter(expense => 
            expense.category_id === budget.category_id &&
            new Date(expense.date) >= periodStart &&
            new Date(expense.date) <= now
          )
          .reduce((sum, expense) => sum + Number(expense.amount), 0) || 0;

        const remaining = Number(budget.amount) - spent;
        const percentage = Number(budget.amount) > 0 ? (spent / Number(budget.amount)) * 100 : 0;
        const isOverBudget = spent > Number(budget.amount);

        return {
          id: budget.id,
          amount: Number(budget.amount),
          category,
          period: budget.period as 'weekly' | 'monthly' | 'yearly',
          startDate: budget.start_date,
          endDate: budget.end_date || undefined,
          isActive: budget.is_active,
          createdAt: budget.created_at,
          spent,
          remaining,
          percentage,
          isOverBudget
        };
      });

      setBudgets(budgetsWithSpending);
    } catch (error: any) {
      console.error('Error fetching budgets:', error);
      toast({
        title: "Error",
        description: "Failed to load budgets. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const addBudget = async (budgetData: Omit<Budget, 'id' | 'createdAt'>) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('budgets')
        .insert({
          user_id: user.id,
          amount: budgetData.amount,
          category_id: budgetData.category.id,
          category_name: budgetData.category.name,
          category_emoji: budgetData.category.emoji,
          period: budgetData.period,
          start_date: budgetData.startDate,
          end_date: budgetData.endDate || null,
          is_active: budgetData.isActive
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Budget created successfully!",
      });

      fetchBudgets();
    } catch (error: any) {
      console.error('Error adding budget:', error);
      toast({
        title: "Error",
        description: "Failed to create budget. Please try again.",
        variant: "destructive",
      });
    }
  };

  const updateBudget = async (id: string, updates: Partial<Budget>) => {
    if (!user) return;

    try {
      const updateData: any = {};
      
      if (updates.amount !== undefined) updateData.amount = updates.amount;
      if (updates.period !== undefined) updateData.period = updates.period;
      if (updates.startDate !== undefined) updateData.start_date = updates.startDate;
      if (updates.endDate !== undefined) updateData.end_date = updates.endDate;
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
      
      if (updates.category) {
        updateData.category_id = updates.category.id;
        updateData.category_name = updates.category.name;
        updateData.category_emoji = updates.category.emoji;
      }

      const { error } = await supabase
        .from('budgets')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Budget updated successfully!",
      });

      fetchBudgets();
    } catch (error: any) {
      console.error('Error updating budget:', error);
      toast({
        title: "Error",
        description: "Failed to update budget. Please try again.",
        variant: "destructive",
      });
    }
  };

  const deleteBudget = async (id: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('budgets')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Budget deleted successfully!",
      });

      fetchBudgets();
    } catch (error: any) {
      console.error('Error deleting budget:', error);
      toast({
        title: "Error",
        description: "Failed to delete budget. Please try again.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user) {
      fetchBudgets();
    }
  }, [user]);

  return {
    budgets,
    loading,
    addBudget,
    updateBudget,
    deleteBudget,
    refetch: fetchBudgets
  };
};