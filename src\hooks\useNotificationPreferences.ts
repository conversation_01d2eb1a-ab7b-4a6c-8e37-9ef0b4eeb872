import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface NotificationPreferences {
  id?: string;
  user_id: string;
  email_notifications_enabled: boolean;
  browser_notifications_enabled: boolean;
  recurring_bill_reminder_enabled: boolean;
  recurring_bill_reminder_days: number;
  expense_reminder_enabled: boolean;
  expense_reminder_hours: number;
  last_expense_logged_at?: string;
}

export const useNotificationPreferences = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchPreferences = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) throw error;

      if (data) {
        setPreferences(data);
      } else {
        // Create default preferences
        const defaultPrefs = {
          user_id: user.id,
          email_notifications_enabled: false,
          browser_notifications_enabled: false,
          recurring_bill_reminder_enabled: true,
          recurring_bill_reminder_days: 1,
          expense_reminder_enabled: true,
          expense_reminder_hours: 24,
        };
        
        const { data: newPrefs, error: createError } = await supabase
          .from('notification_preferences')
          .insert(defaultPrefs)
          .select()
          .single();

        if (createError) throw createError;
        setPreferences(newPrefs);
      }
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      toast({
        title: "Error",
        description: "Failed to load notification preferences",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updatePreferences = async (updates: Partial<NotificationPreferences>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user || !preferences) return;

      const { error } = await supabase
        .from('notification_preferences')
        .update(updates)
        .eq('user_id', user.id);

      if (error) throw error;

      setPreferences({ ...preferences, ...updates });
      
      toast({
        title: "Success",
        description: "Notification preferences updated",
      });

      // Request browser notification permission if enabled
      if (updates.browser_notifications_enabled && 'Notification' in window) {
        await Notification.requestPermission();
      }
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      toast({
        title: "Error",
        description: "Failed to update notification preferences",
        variant: "destructive",
      });
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  };

  const showBrowserNotification = (title: string, options?: NotificationOptions) => {
    if ('Notification' in window && Notification.permission === 'granted' && preferences?.browser_notifications_enabled) {
      new Notification(title, {
        icon: '/favicon.ico',
        ...options,
      });
    }
  };

  useEffect(() => {
    fetchPreferences();
  }, []);

  return {
    preferences,
    loading,
    updatePreferences,
    requestNotificationPermission,
    showBrowserNotification,
    refetch: fetchPreferences,
  };
};