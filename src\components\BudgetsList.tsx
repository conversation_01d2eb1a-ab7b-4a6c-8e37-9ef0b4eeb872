import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { PiggyBank, Plus, Trash2, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';
import { useBudgets } from '@/hooks/useBudgets';
import { useLanguage } from '@/hooks/useLanguage';
import { BudgetForm } from './BudgetForm';

export const BudgetsList = () => {
  const [showForm, setShowForm] = useState(false);
  const { budgets, loading, updateBudget, deleteBudget } = useBudgets();
  const { language, isRTL } = useLanguage();

  const translations = {
    en: {
      title: "Budget Management",
      subtitle: "Track your spending against your budgets",
      addNew: "Add Budget",
      spent: "Spent",
      remaining: "Remaining",
      overBudget: "Over Budget",
      onTrack: "On Track",
      warning: "Warning",
      noBudgets: "No budgets set",
      noBudgetsDesc: "Create your first budget to track your spending and stay on top of your finances.",
      weekly: "Weekly",
      monthly: "Monthly",
      yearly: "Yearly",
      delete: "Delete",
      period: "Period"
    },
    ar: {
      title: "إدارة الميزانية",
      subtitle: "تتبع إنفاقك مقارنة بميزانياتك",
      addNew: "إضافة ميزانية",
      spent: "تم إنفاقه",
      remaining: "المتبقي",
      overBudget: "تجاوز الميزانية",
      onTrack: "في المسار الصحيح",
      warning: "تحذير",
      noBudgets: "لم يتم تعيين ميزانيات",
      noBudgetsDesc: "أنشئ أول ميزانية لك لتتبع إنفاقك والبقاء على اطلاع على أموالك.",
      weekly: "أسبوعي",
      monthly: "شهري",
      yearly: "سنوي",
      delete: "حذف",
      period: "الفترة"
    }
  };

  const t = translations[language];

  const toggleActiveStatus = async (id: string, currentStatus: boolean) => {
    await updateBudget(id, { isActive: !currentStatus });
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this budget?')) {
      await deleteBudget(id);
    }
  };

  const getBudgetStatus = (budget: any) => {
    if (budget.isOverBudget) return { text: t.overBudget, variant: 'destructive' as const };
    if (budget.percentage > 80) return { text: t.warning, variant: 'secondary' as const };
    return { text: t.onTrack, variant: 'default' as const };
  };

  const getPeriodText = (period: string) => {
    switch (period) {
      case 'weekly': return t.weekly;
      case 'yearly': return t.yearly;
      default: return t.monthly;
    }
  };

  if (loading) {
    return (
      <Card className="bg-gradient-card border-0 shadow-card">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <Card className="bg-gradient-card border-0 shadow-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                <PiggyBank className="h-5 w-5 text-primary" />
                {t.title}
              </CardTitle>
              <p className="text-muted-foreground text-sm mt-1">{t.subtitle}</p>
            </div>
            <Button
              onClick={() => setShowForm(true)}
              className="bg-gradient-primary hover:opacity-90 gap-2"
            >
              <Plus className="h-4 w-4" />
              {t.addNew}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {budgets.length === 0 ? (
        <Card className="bg-gradient-card border-0 shadow-card">
          <CardContent className="p-8 text-center">
            <PiggyBank className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t.noBudgets}</h3>
            <p className="text-muted-foreground mb-4">{t.noBudgetsDesc}</p>
            <Button
              onClick={() => setShowForm(true)}
              className="bg-gradient-primary hover:opacity-90 gap-2"
            >
              <Plus className="h-4 w-4" />
              {t.addNew}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {budgets.map((budget) => {
            const status = getBudgetStatus(budget);
            
            return (
              <Card key={budget.id} className="bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{budget.category.emoji}</div>
                        <div>
                          <h3 className="font-semibold text-lg">{budget.category.name}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{t.period}: {getPeriodText(budget.period)}</span>
                            <Badge variant={status.variant}>{status.text}</Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={budget.isActive}
                          onCheckedChange={() => toggleActiveStatus(budget.id, budget.isActive)}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(budget.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Budget Progress */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>{t.spent}: ${budget.spent.toFixed(2)}</span>
                        <span>{t.remaining}: ${budget.remaining.toFixed(2)}</span>
                      </div>
                      
                      <Progress 
                        value={Math.min(budget.percentage, 100)} 
                        className="h-3"
                      />
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {budget.percentage.toFixed(1)}% of ${budget.amount.toFixed(2)}
                        </span>
                        
                        {budget.isOverBudget && (
                          <div className="flex items-center gap-1 text-destructive text-sm">
                            <AlertTriangle className="h-3 w-3" />
                            <TrendingUp className="h-3 w-3" />
                          </div>
                        )}
                        
                        {!budget.isOverBudget && budget.percentage <= 80 && (
                          <div className="flex items-center gap-1 text-primary text-sm">
                            <TrendingDown className="h-3 w-3" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      <BudgetForm 
        isOpen={showForm}
        onClose={() => setShowForm(false)}
      />
    </div>
  );
};