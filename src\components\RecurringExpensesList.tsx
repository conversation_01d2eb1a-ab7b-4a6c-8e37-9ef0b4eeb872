import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Repeat, Plus, Trash2, Calendar, Clock } from 'lucide-react';
import { useRecurringExpenses } from '@/hooks/useRecurringExpenses';
import { useLanguage } from '@/hooks/useLanguage';
import { RecurringExpenseForm } from './RecurringExpenseForm';
import { format } from 'date-fns';

export const RecurringExpensesList = () => {
  const [showForm, setShowForm] = useState(false);
  const { recurringExpenses, loading, updateRecurringExpense, deleteRecurringExpense } = useRecurringExpenses();
  const { language, isRTL } = useLanguage();

  const translations = {
    en: {
      title: "Recurring Expenses",
      subtitle: "Manage your automatic recurring expenses",
      addNew: "Add Recurring Expense",
      nextDue: "Next Due",
      frequency: "Frequency",
      active: "Active",
      inactive: "Inactive",
      noRecurring: "No recurring expenses",
      noRecurringDesc: "Set up automatic recurring expenses to never miss a payment.",
      delete: "Delete",
      every: "Every",
      daily: "day(s)",
      weekly: "week(s)",
      monthly: "month(s)",
      yearly: "year(s)",
      ends: "Ends"
    },
    ar: {
      title: "المصروفات المتكررة",
      subtitle: "إدارة مصروفاتك المتكررة التلقائية",
      addNew: "إضافة مصروف متكرر",
      nextDue: "الاستحقاق التالي",
      frequency: "التكرار",
      active: "نشط",
      inactive: "غير نشط",
      noRecurring: "لا توجد مصروفات متكررة",
      noRecurringDesc: "قم بإعداد المصروفات المتكررة التلقائية لعدم تفويت أي دفعة.",
      delete: "حذف",
      every: "كل",
      daily: "يوم",
      weekly: "أسبوع",
      monthly: "شهر",
      yearly: "سنة",
      ends: "ينتهي"
    }
  };

  const t = translations[language];

  const toggleActiveStatus = async (id: string, currentStatus: boolean) => {
    await updateRecurringExpense(id, { is_active: !currentStatus });
  };

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this recurring expense?')) {
      await deleteRecurringExpense(id);
    }
  };

  const getFrequencyText = (frequency: string, interval: number) => {
    const frequencyMap = {
      daily: t.daily,
      weekly: t.weekly,
      monthly: t.monthly,
      yearly: t.yearly
    };
    return `${t.every} ${interval} ${frequencyMap[frequency as keyof typeof frequencyMap] || frequency}`;
  };

  if (loading) {
    return (
      <Card className="bg-gradient-card border-0 shadow-card">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <Card className="bg-gradient-card border-0 shadow-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                <Repeat className="h-5 w-5 text-primary" />
                {t.title}
              </CardTitle>
              <p className="text-muted-foreground text-sm mt-1">{t.subtitle}</p>
            </div>
            <Button
              onClick={() => setShowForm(true)}
              className="bg-gradient-primary hover:opacity-90 gap-2"
            >
              <Plus className="h-4 w-4" />
              {t.addNew}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {recurringExpenses.length === 0 ? (
        <Card className="bg-gradient-card border-0 shadow-card">
          <CardContent className="p-8 text-center">
            <Repeat className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t.noRecurring}</h3>
            <p className="text-muted-foreground mb-4">{t.noRecurringDesc}</p>
            <Button
              onClick={() => setShowForm(true)}
              className="bg-gradient-primary hover:opacity-90 gap-2"
            >
              <Plus className="h-4 w-4" />
              {t.addNew}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {recurringExpenses.map((expense) => (
            <Card key={expense.id} className="bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{expense.category_emoji}</div>
                      <div>
                        <h3 className="font-semibold text-lg">{expense.category_name}</h3>
                        <p className="text-2xl font-bold text-primary">${expense.amount.toFixed(2)}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant={expense.is_active ? "default" : "secondary"}>
                        {expense.is_active ? t.active : t.inactive}
                      </Badge>
                      <Switch
                        checked={expense.is_active}
                        onCheckedChange={() => toggleActiveStatus(expense.id, expense.is_active)}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(expense.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{t.frequency}:</span>
                      <span>{getFrequencyText(expense.frequency, expense.frequency_interval)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">{t.nextDue}:</span>
                      <span>{format(new Date(expense.next_due_date), 'MMM dd, yyyy')}</span>
                    </div>
                    
                    {expense.end_date && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">{t.ends}:</span>
                        <span>{format(new Date(expense.end_date), 'MMM dd, yyyy')}</span>
                      </div>
                    )}
                  </div>

                  {/* Note */}
                  {expense.note && (
                    <div className="p-3 bg-secondary/30 rounded-lg">
                      <p className="text-sm text-muted-foreground">{expense.note}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <RecurringExpenseForm 
        isOpen={showForm}
        onClose={() => setShowForm(false)}
      />
    </div>
  );
};