-- Create recurring_expenses table
CREATE TABLE public.recurring_expenses (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  category_id TEXT NOT NULL,
  category_name TEXT NOT NULL,
  category_emoji TEXT NOT NULL,
  note TEXT NOT NULL DEFAULT '',
  frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'yearly')),
  frequency_interval INTEGER NOT NULL DEFAULT 1, -- e.g., every 2 weeks = weekly with interval 2
  start_date DATE NOT NULL,
  next_due_date DATE NOT NULL,
  end_date DATE, -- optional end date
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.recurring_expenses ENABLE ROW LEVEL SECURITY;

-- Create policies for user access
CREATE POLICY "Users can view their own recurring expenses" 
ON public.recurring_expenses 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own recurring expenses" 
ON public.recurring_expenses 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own recurring expenses" 
ON public.recurring_expenses 
FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own recurring expenses" 
ON public.recurring_expenses 
FOR DELETE 
USING (auth.uid() = user_id);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_recurring_expenses_updated_at
  BEFORE UPDATE ON public.recurring_expenses
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Enable pg_cron extension
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create function to calculate next due date
CREATE OR REPLACE FUNCTION public.calculate_next_due_date(
  current_date DATE,
  frequency TEXT,
  frequency_interval INTEGER
) RETURNS DATE AS $$
BEGIN
  CASE frequency
    WHEN 'daily' THEN
      RETURN current_date + (frequency_interval || ' days')::INTERVAL;
    WHEN 'weekly' THEN
      RETURN current_date + (frequency_interval || ' weeks')::INTERVAL;
    WHEN 'monthly' THEN
      RETURN current_date + (frequency_interval || ' months')::INTERVAL;
    WHEN 'yearly' THEN
      RETURN current_date + (frequency_interval || ' years')::INTERVAL;
    ELSE
      RETURN current_date + '1 month'::INTERVAL;
  END CASE;
END;
$$ LANGUAGE plpgsql;