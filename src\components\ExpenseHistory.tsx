import { useState } from 'react';
import { Search, Filter, Calendar, Trash2, Edit3, FileText, Image } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useExpenses } from '@/hooks/useExpenses';
import { useLanguage } from '@/hooks/useLanguage';
import { Expense } from '@/types/expense';

export const ExpenseHistory = () => {
  const { expenses, deleteExpense, filters, setFilters, categories } = useExpenses();
  const { language, isRTL } = useLanguage();
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);

  const translations = {
    en: {
      title: "Expense History",
      search: "Search expenses...",
      filter: "Filter",
      all: "All Categories",
      noExpenses: "No expenses found",
      addSome: "Add some expenses to see them here",
      amount: "Amount",
      category: "Category",
      note: "Note",
      date: "Date",
      edit: "Edit",
      delete: "Delete",
      confirmDelete: "Are you sure you want to delete this expense?",
      cancel: "Cancel"
    },
    ar: {
      title: "سجل المصروفات",
      search: "البحث في المصروفات...",
      filter: "تصفية",
      all: "جميع الفئات",
      noExpenses: "لم يتم العثور على مصروفات",
      addSome: "أضف بعض المصروفات لرؤيتها هنا",
      amount: "المبلغ",
      category: "الفئة",
      note: "الملاحظة",
      date: "التاريخ",
      edit: "تعديل",
      delete: "حذف",
      confirmDelete: "هل أنت متأكد من حذف هذا المصروف؟",
      cancel: "إلغاء"
    }
  };

  const t = translations[language];

  const handleSearch = (searchTerm: string) => {
    setFilters({ ...filters, searchTerm });
  };

  const handleCategoryFilter = (categoryId: string) => {
    setFilters({ 
      ...filters, 
      category: categoryId === 'all' ? undefined : categoryId 
    });
  };

  const handleDateFilter = (startDate: string, endDate?: string) => {
    setFilters({ ...filters, startDate, endDate });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleDeleteExpense = (expense: Expense) => {
    if (confirm(t.confirmDelete)) {
      deleteExpense(expense.id);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <h2 className="text-2xl font-bold">{t.title}</h2>
        
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <div className="relative">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground ${isRTL ? 'right-3' : 'left-3'}`} />
            <Input
              placeholder={t.search}
              className={`${isRTL ? 'pr-10' : 'pl-10'} w-full sm:w-64`}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          
          <select
            className="px-3 py-2 border border-border rounded-md bg-card text-sm"
            onChange={(e) => handleCategoryFilter(e.target.value)}
            value={filters.category || 'all'}
          >
            <option value="all">{t.all}</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.emoji} {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Date Range Filter */}
      <Card className="p-4 bg-gradient-card border-0 shadow-card">
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <div className="flex flex-col sm:flex-row gap-3">
            <div>
              <label className="text-sm text-muted-foreground">From:</label>
              <Input
                type="date"
                value={filters.startDate || ''}
                onChange={(e) => handleDateFilter(e.target.value, filters.endDate)}
                className="mt-1"
              />
            </div>
            <div>
              <label className="text-sm text-muted-foreground">To:</label>
              <Input
                type="date"
                value={filters.endDate || ''}
                onChange={(e) => handleDateFilter(filters.startDate || '', e.target.value)}
                className="mt-1"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setFilters({})}
              className="mt-6 sm:mt-6"
            >
              Clear Filters
            </Button>
          </div>
        </div>
      </Card>

      {/* Expenses List */}
      {expenses.length === 0 ? (
        <Card className="p-8 text-center bg-gradient-card border-0 shadow-card">
          <Filter className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-semibold mb-2">{t.noExpenses}</h3>
          <p className="text-muted-foreground">{t.addSome}</p>
        </Card>
      ) : (
        <div className="space-y-3">
          {expenses.map((expense) => (
            <Card 
              key={expense.id} 
              className="p-4 bg-gradient-card border-0 shadow-card hover:shadow-floating transition-all duration-300 animate-fade-in"
            >
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-4 flex-1">
                  <div className="text-3xl">{expense.category.emoji}</div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold truncate">{expense.category.name}</h3>
                      <Badge variant="outline" className="text-xs">
                        ${expense.amount.toFixed(2)}
                      </Badge>
                      {expense.invoiceUrl && (
                        <Badge variant="secondary" className="text-xs gap-1">
                          <FileText className="h-3 w-3" />
                          Receipt
                        </Badge>
                      )}
                    </div>
                    
                    {expense.note && (
                      <p className="text-sm text-muted-foreground truncate mb-1">
                        {expense.note}
                      </p>
                    )}
                    
                    <p className="text-xs text-muted-foreground">
                      {formatDate(expense.date)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setSelectedExpense(expense)}
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDeleteExpense(expense)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};