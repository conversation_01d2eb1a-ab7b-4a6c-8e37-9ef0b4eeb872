import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.51.0';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface RecurringExpense {
  id: string;
  user_id: string;
  amount: number;
  category_id: string;
  category_name: string;
  category_emoji: string;
  frequency: string;
  frequency_interval: number;
  next_due_date: string;
  end_date: string | null;
  note: string;
  is_active: boolean;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('Processing recurring expenses...');

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const today = new Date().toISOString().split('T')[0];
    console.log('Processing for date:', today);

    // Get all active recurring expenses that are due today or overdue
    const { data: dueExpenses, error: fetchError } = await supabase
      .from('recurring_expenses')
      .select('*')
      .eq('is_active', true)
      .lte('next_due_date', today);

    if (fetchError) {
      console.error('Error fetching due expenses:', fetchError);
      throw fetchError;
    }

    console.log(`Found ${dueExpenses?.length || 0} due expenses`);

    let processedCount = 0;
    let errorCount = 0;

    for (const expense of dueExpenses || []) {
      try {
        // Check if end date has passed
        if (expense.end_date && expense.end_date < today) {
          console.log(`Deactivating expired recurring expense: ${expense.id}`);
          await supabase
            .from('recurring_expenses')
            .update({ is_active: false })
            .eq('id', expense.id);
          continue;
        }

        // Create the expense entry
        const { error: insertError } = await supabase
          .from('expenses')
          .insert({
            user_id: expense.user_id,
            amount: expense.amount,
            category_id: expense.category_id,
            category_name: expense.category_name,
            category_emoji: expense.category_emoji,
            date: expense.next_due_date,
            note: expense.note + ' (Auto-generated from recurring expense)'
          });

        if (insertError) {
          console.error(`Error creating expense for recurring expense ${expense.id}:`, insertError);
          errorCount++;
          continue;
        }

        // Calculate next due date
        const { data: nextDueDate, error: calcError } = await supabase
          .rpc('calculate_next_due_date', {
            base_date: expense.next_due_date,
            frequency: expense.frequency,
            frequency_interval: expense.frequency_interval
          });

        if (calcError) {
          console.error(`Error calculating next due date for ${expense.id}:`, calcError);
          errorCount++;
          continue;
        }

        // Update the recurring expense with new next due date
        const { error: updateError } = await supabase
          .from('recurring_expenses')
          .update({ next_due_date: nextDueDate })
          .eq('id', expense.id);

        if (updateError) {
          console.error(`Error updating recurring expense ${expense.id}:`, updateError);
          errorCount++;
          continue;
        }

        processedCount++;
        console.log(`Successfully processed recurring expense: ${expense.id}`);

      } catch (error) {
        console.error(`Error processing recurring expense ${expense.id}:`, error);
        errorCount++;
      }
    }

    const result = {
      success: true,
      processed: processedCount,
      errors: errorCount,
      total: dueExpenses?.length || 0,
      date: today
    };

    console.log('Processing complete:', result);

    return new Response(
      JSON.stringify(result),
      {
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        },
        status: 200
      }
    );

  } catch (error) {
    console.error('Error in process-recurring-expenses function:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        },
        status: 500
      }
    );
  }
});