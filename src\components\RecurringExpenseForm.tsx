import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Repeat, Save, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useRecurringExpenses } from '@/hooks/useRecurringExpenses';
import { useLanguage } from '@/hooks/useLanguage';
import { DEFAULT_CATEGORIES } from '@/types/expense';

interface RecurringExpenseFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export const RecurringExpenseForm = ({ isOpen, onClose }: RecurringExpenseFormProps) => {
  const [amount, setAmount] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [frequency, setFrequency] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');
  const [frequencyInterval, setFrequencyInterval] = useState('1');
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [note, setNote] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const { addRecurringExpense } = useRecurringExpenses();
  const { language, isRTL } = useLanguage();

  const translations = {
    en: {
      title: "Create Recurring Expense",
      amount: "Amount",
      category: "Category",
      frequency: "Frequency",
      interval: "Every",
      startDate: "Start Date",
      endDate: "End Date (Optional)",
      note: "Note",
      save: "Create",
      cancel: "Cancel",
      selectCategory: "Select a category",
      selectDate: "Select date",
      daily: "Day(s)",
      weekly: "Week(s)",
      monthly: "Month(s)",
      yearly: "Year(s)",
      amountPlaceholder: "Enter amount",
      notePlaceholder: "Add a note (optional)"
    },
    ar: {
      title: "إنشاء مصروف متكرر",
      amount: "المبلغ",
      category: "الفئة",
      frequency: "التكرار",
      interval: "كل",
      startDate: "تاريخ البداية",
      endDate: "تاريخ النهاية (اختياري)",
      note: "ملاحظة",
      save: "إنشاء",
      cancel: "إلغاء",
      selectCategory: "اختر فئة",
      selectDate: "اختر تاريخ",
      daily: "يوم",
      weekly: "أسبوع",
      monthly: "شهر",
      yearly: "سنة",
      amountPlaceholder: "أدخل المبلغ",
      notePlaceholder: "أضف ملاحظة (اختياري)"
    }
  };

  const t = translations[language];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount || !categoryId || !startDate) return;

    setSubmitting(true);
    
    const selectedCategory = DEFAULT_CATEGORIES.find(cat => cat.id === categoryId);
    if (!selectedCategory) return;

    await addRecurringExpense({
      amount: parseFloat(amount),
      category: selectedCategory,
      frequency,
      frequencyInterval: parseInt(frequencyInterval),
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate?.toISOString().split('T')[0],
      note
    });

    // Reset form
    setAmount('');
    setCategoryId('');
    setFrequency('monthly');
    setFrequencyInterval('1');
    setStartDate(undefined);
    setEndDate(undefined);
    setNote('');
    setSubmitting(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`max-w-md bg-gradient-card border-0 shadow-floating ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            <Repeat className="h-5 w-5 text-primary" />
            {t.title}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="amount">{t.amount}</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder={t.amountPlaceholder}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">{t.category}</Label>
            <Select value={categoryId} onValueChange={setCategoryId} required>
              <SelectTrigger>
                <SelectValue placeholder={t.selectCategory} />
              </SelectTrigger>
              <SelectContent>
                {DEFAULT_CATEGORIES.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <span className="flex items-center gap-2">
                      <span>{category.emoji}</span>
                      <span>{category.name}</span>
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>{t.interval}</Label>
              <Input
                type="number"
                min="1"
                value={frequencyInterval}
                onChange={(e) => setFrequencyInterval(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label>{t.frequency}</Label>
              <Select value={frequency} onValueChange={(value: any) => setFrequency(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">{t.daily}</SelectItem>
                  <SelectItem value="weekly">{t.weekly}</SelectItem>
                  <SelectItem value="monthly">{t.monthly}</SelectItem>
                  <SelectItem value="yearly">{t.yearly}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label>{t.startDate}</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !startDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, "PPP") : <span>{t.selectDate}</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>{t.endDate}</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !endDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? format(endDate, "PPP") : <span>{t.selectDate}</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  disabled={(date) => startDate ? date < startDate : false}
                  initialFocus
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label htmlFor="note">{t.note}</Label>
            <Textarea
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder={t.notePlaceholder}
              rows={3}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={submitting}
            >
              <X className="mr-2 h-4 w-4" />
              {t.cancel}
            </Button>
            <Button
              type="submit"
              disabled={!amount || !categoryId || !startDate || submitting}
              className="flex-1 bg-gradient-primary hover:opacity-90"
            >
              <Save className="mr-2 h-4 w-4" />
              {submitting ? "..." : t.save}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};