// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wbjkmobvzvlztcklxjgw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indiamttb2J2enZsenRja2x4amd3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2NDMyNTMsImV4cCI6MjA2ODIxOTI1M30.i5xHy2uHF7_xG_WsEzig8A_UBBQWiFkiSAGFPyLKqfM";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});